<?php

namespace App\Http\Livewire\Accounting\Proposal;

use Livewire\Component;
use App\Services\Finance\FinanceProposalService;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Crypt;

class ProposalCreate extends Component
{
    // Form data properties
    public $customers = [];
    public $billing_types = [];
    public $categories = [];
    public $templates = [];
    public $items_list = [];
    public $item_types = [];

    // Form fields
    public $proposal_type;
    public $customer_id;
    public $issue_date;
    public $category_id;
    public $proposal_template;
    public $proposal_number;

    // Items array
    public $items = [];

    // Calculated totals
    public $subtotal = 0;
    public $discount_total = 0;
    public $tax_total = 0;
    public $total = 0;

    // Loading and error states
    public $loading = false;
    public $saving = false;
    public $error = null;

    protected $rules = [
        'proposal_type' => 'required',
        'customer_id' => 'required',
        'issue_date' => 'required|date',
        'category_id' => 'required',
        'proposal_template' => 'required',
        'items' => 'required|array|min:1',
        'items.*.item' => 'required',
        'items.*.quantity' => 'required|numeric|min:1',
        'items.*.price' => 'required|numeric|min:0',
    ];

    protected $messages = [
        'proposal_type.required' => 'Proposal type is required.',
        'customer_id.required' => 'Customer is required.',
        'issue_date.required' => 'Issue date is required.',
        'category_id.required' => 'Category is required.',
        'proposal_template.required' => 'Template is required.',
        'items.required' => 'At least one item is required.',
        'items.min' => 'At least one item is required.',
        'items.*.item.required' => 'Item is required.',
        'items.*.quantity.required' => 'Quantity is required.',
        'items.*.quantity.min' => 'Quantity must be at least 1.',
        'items.*.price.required' => 'Price is required.',
        'items.*.price.min' => 'Price must be at least 0.',
    ];

    public function mount()
    {
        $this->fetchFormData();
        $this->initializeItems();
    }

    public function fetchFormData()
    {
        $this->loading = true;
        $this->error = null;

        try {
            $service = app(FinanceProposalService::class);
            $response = $service->dropdownlist();

            if (isset($response['status']) && $response['status'] === 'success' && isset($response['data'])) {
                $data = $response['data'];

                $this->customers = $data['customers'] ?? [];
                $this->billing_types = $data['billing_types'] ?? [];
                $this->categories = $data['category'] ?? [];
                $this->templates = $data['templates'] ?? [];
                $this->items_list = $data['items'] ?? [];
                $this->item_types = $data['item_types'] ?? [];
                $this->proposal_number = $data['proposal_number'] ?? '';
            } else {
                $this->error = $response['message'] ?? __('accounting.errors.something_went_wrong');
            }
        } catch (\Exception $e) {
            $this->error = __('accounting.errors.something_went_wrong');
        } finally {
            $this->loading = false;
        }
    }

    public function initializeItems()
    {
        $this->items = [
            [
                'product_type' => '',
                'item' => '',
                'quantity' => 1,
                'price' => 0,
                'tax' => 0,
                'discount' => 0,
                'description' => ''
            ]
        ];
        $this->calculateTotals();
    }

    public function addItem()
    {
        $this->items[] = [
            'product_type' => '',
            'item' => '',
            'quantity' => 1,
            'price' => 0,
            'tax' => 0,
            'discount' => 0,
            'description' => ''
        ];
        $this->calculateTotals();
    }

    public function removeItem($index)
    {
        if (count($this->items) > 1) {
            unset($this->items[$index]);
            $this->items = array_values($this->items); // reindex the array
            $this->calculateTotals();
        }
    }

    public function updatedItems($value, $key)
    {
        // Auto-populate item details when item is selected
        if (Str::endsWith($key, '.item')) {
            [$index] = explode('.', $key);
            $selectedItemId = $this->items[$index]['item'] ?? null;

            if ($selectedItemId) {
                // Find the selected item in items_list
                $selectedItem = collect($this->items_list)->firstWhere('id', $selectedItemId);
                if ($selectedItem) {
                    $this->items[$index]['price'] = $selectedItem['price'] ?? 0;
                    $this->items[$index]['tax'] = $selectedItem['tax_rate'] ?? 0;
                }
            }
        }

        $this->calculateTotals();
    }

    public function calculateTotals()
    {
        $this->subtotal = 0;
        $this->discount_total = 0;
        $this->tax_total = 0;
        $this->total = 0;

        foreach ($this->items as $item) {
            $qty = $item['quantity'] ?? 1;
            $price = $item['price'] ?? 0;
            $discount = $item['discount'] ?? 0;
            $taxRate = $item['tax'] ?? 0;

            $lineTotal = $qty * $price;
            $lineDiscount = $discount;
            $lineAfterDiscount = $lineTotal - $lineDiscount;
            $lineTax = ($lineAfterDiscount * $taxRate) / 100;

            $this->subtotal += $lineTotal;
            $this->discount_total += $lineDiscount;
            $this->tax_total += $lineTax;
            $this->total += ($lineAfterDiscount + $lineTax);
        }
    }

    public function calculateItemTotal($index)
    {
        $item = $this->items[$index] ?? [];
        $qty = $item['quantity'] ?? 1;
        $price = $item['price'] ?? 0;
        $discount = $item['discount'] ?? 0;
        $taxRate = $item['tax'] ?? 0;

        $amount = ($qty * $price) - $discount;
        $taxAmount = ($amount * $taxRate) / 100;

        return $amount + $taxAmount;
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            // Prepare items data
            $itemsData = [];
            foreach ($this->items as $item) {
                $itemsData[] = [
                    'product_type' => $item['product_type'],
                    'item' => $item['item'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'tax' => $item['tax'],
                    'discount' => $item['discount'],
                    'description' => $item['description'] ?? '',
                ];
            }

            // Prepare proposal data
            $proposalData = [
                'proposal_type' => $this->proposal_type,
                'customer_id' => $this->customer_id,
                'issue_date' => $this->issue_date,
                'category_id' => $this->category_id,
                'proposal_template' => $this->proposal_template,
                'items' => $itemsData,
            ];

            $service = app(FinanceProposalService::class);
            $response = $service->create($proposalData);

            if (isset($response['status']) && $response['status'] === 'success') {
                $this->dispatchBrowserEvent('show-toast', [
                    'type' => 'success',
                    'message' => $response['message'] ?? __('accounting.proposals.created_successfully')
                ]);

                // Emit refresh event to parent components
                $this->emit('proposalCreated');

                // Redirect to customer view page with the customer ID
                return redirect()->route('finance.customers.view', ['id' => Crypt::encrypt($this->customer_id)]);
            } else {
                $this->dispatchBrowserEvent('show-toast', [
                    'type' => 'error',
                    'message' => $response['message'] ?? __('accounting.errors.something_went_wrong')
                ]);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => __('accounting.validation_errors')
            ]);
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => __('accounting.errors.something_went_wrong')
            ]);
        } finally {
            $this->saving = false;
        }
    }

    public function resetForm()
    {
        $this->reset([
            'proposal_type',
            'customer_id',
            'issue_date',
            'category_id',
            'proposal_template'
        ]);

        $this->initializeItems();
        $this->resetValidation();
    }

    public function render()
    {
        return view('livewire.accounting.proposal.proposal-create');
    }
}
